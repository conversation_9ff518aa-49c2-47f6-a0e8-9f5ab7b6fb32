-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: mysql-6eeec90-vankay966-ba73.b.aivencloud.com    Database: defaultdb
-- ------------------------------------------------------
-- Server version	8.0.35

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ 'ed6efb7e-6e97-11f0-a8f3-862ccfb02bb6:1-403';

--
-- Table structure for table `agent_pricing_plans`
--

DROP TABLE IF EXISTS `agent_pricing_plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_pricing_plans` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('per_usage','time_based') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `usage_count` int DEFAULT NULL,
  `price_per_usage` decimal(10,2) DEFAULT NULL,
  `duration_days` int DEFAULT NULL,
  `price_per_period` decimal(10,2) DEFAULT NULL,
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'CNY',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `agent_pricing_plans_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `agent_pricing_plans`
--

LOCK TABLES `agent_pricing_plans` WRITE;
/*!40000 ALTER TABLE `agent_pricing_plans` DISABLE KEYS */;
INSERT INTO `agent_pricing_plans` VALUES ('mdpxbymbi926f1zzoq','mdpk4ezijzy5fxs26v','方案1','per_usage',10,20.00,NULL,NULL,'CNY','去啊是的你不卡就是在打开v你上次看C就 ',1,'2025-07-30 20:10:20','2025-07-30 20:10:20'),('mdpxjql19om5va0o3di','mdpk4ezijzy5fxs26v','阿斯顿撒打算','time_based',NULL,NULL,15,30.00,'CNY','谔谔温热风扇的各项的方向发展',1,'2025-07-30 20:16:23','2025-07-30 20:16:23'),('mdsoae1b3kgvc9imwqy','mdrd7yzbvu1s7jlmmiq','去额外','per_usage',10,22.00,NULL,NULL,'CNY','123123',1,'2025-08-01 18:20:29','2025-08-01 18:20:29'),('mdsobx64yjdua3k5vmi','mdpi8ky2jhq5j1sfoy','为 ','per_usage',12,12.00,NULL,NULL,'CNY','12',1,'2025-08-01 18:21:40','2025-08-01 18:21:40'),('redemption_duration','mdot22bk1p9f2s5608y','兑换码时长套餐','time_based',NULL,NULL,0,0.00,'CNY','通过兑换码获得的时长套餐',1,'2025-07-31 23:51:08','2025-07-31 23:51:08'),('redemption_usage','mdot22bk1p9f2s5608y','兑换码次数套餐','per_usage',NULL,NULL,0,0.00,'CNY','通过兑换码获得的次数套餐',1,'2025-07-31 23:51:08','2025-07-31 23:51:08');
/*!40000 ALTER TABLE `agent_pricing_plans` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `agent_usage_logs`
--

DROP TABLE IF EXISTS `agent_usage_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agent_usage_logs` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `subscription_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `session_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `usage_type` enum('chat','generation') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'chat',
  `tokens_used` int DEFAULT '0',
  `cost` decimal(10,4) DEFAULT '0.0000',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `usage_count` int DEFAULT '1' COMMENT '本次使用次数',
  `expiry_date` timestamp NULL DEFAULT NULL COMMENT '截止时间，用于定时定价模式',
  `remaining_count` int DEFAULT NULL COMMENT '剩余使用次数，NULL表示无限制（已付费）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_subscription_id` (`subscription_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_expiry_date` (`expiry_date`),
  KEY `idx_remaining_count` (`remaining_count`),
  CONSTRAINT `agent_usage_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `agent_usage_logs_ibfk_2` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `agent_usage_logs_ibfk_3` FOREIGN KEY (`subscription_id`) REFERENCES `user_agent_subscriptions` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `agent_usage_logs`
--

LOCK TABLES `agent_usage_logs` WRITE;
/*!40000 ALTER TABLE `agent_usage_logs` DISABLE KEYS */;
INSERT INTO `agent_usage_logs` VALUES ('mdpm2ht6ghk74zqxa6','mdoi3uqorptmrc7zwr','mdpk4ezijzy5fxs26v',NULL,'session_1753855249029_9z17ajvu8','chat',0,0.0000,'2025-07-30 14:55:03',6,NULL,94),('mdpojjwa1xkcvipswsw','mdpexo3f13xs4mlm3wsi','mdpjpquswepuel80c6s',NULL,'session_1753862232320_h7qmscjgo','chat',0,0.0000,'2025-07-30 16:04:18',1,NULL,NULL),('mdpov6lfzutmngesdz7','mdpexo3f13xs4mlm3wsi','mdpk4ezijzy5fxs26v','mds4ojrahd4ba1zb7c','session_1754050817848_68gfa582e','chat',0,0.0000,'2025-07-30 16:13:21',24,'2025-08-07 09:11:37',0),('mdpt8skn0x6jxpe13o4d','mdpexo3f13xs4mlm3wsi','mdpi8ky2jhq5j1sfoy',NULL,'session_1753862248175_aljxo5h5v','chat',0,0.0000,'2025-07-30 18:15:54',3,NULL,2),('mdq1oximzdy1jo8m2t','mdoi3uqorptmrc7zwr','mdpi8ky2jhq5j1sfoy',NULL,'session_1753856976841_dpvzlttf6','chat',0,0.0000,'2025-07-30 22:12:24',2,NULL,3),('mds589q81j4ecx6ver4i','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','mdrkoingmenblkg40oo','session_1754039064248_67mlmoukp','chat',0,0.0000,'2025-08-01 09:26:57',5,'2025-08-05 10:15:54',5),('mds5e94h81ygdfv544e','mdpexo3f13xs4mlm3wsi','mdrd5nqnht9t1xmkhve',NULL,'session_1754011374822_zda7eg39a','chat',0,0.0000,'2025-08-01 09:31:36',1,NULL,4);
/*!40000 ALTER TABLE `agent_usage_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `agents`
--

DROP TABLE IF EXISTS `agents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `agents` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `usage_instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'general',
  `agent_type` enum('coze','builtin') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'coze',
  `system_prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'gpt-3.5-turbo',
  `temperature` decimal(3,2) DEFAULT '0.70',
  `max_tokens` int DEFAULT '2000',
  `is_favorite` tinyint(1) DEFAULT '0',
  `usage_count` int DEFAULT '0',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `coze_api_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `coze_bot_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `coze_user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `secret_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `enabled` tinyint(1) DEFAULT '1',
  `trial_usage_count` int DEFAULT '3' COMMENT '试用次数，用户可以免费试用的次数',
  `preset_prompts` json DEFAULT NULL COMMENT '预设提示词列表，存储为JSON数组',
  `purchase_link` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_trial_usage_count` (`trial_usage_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `agents`
--

LOCK TABLES `agents` WRITE;
/*!40000 ALTER TABLE `agents` DISABLE KEYS */;
INSERT INTO `agents` VALUES ('mdot22bk1p9f2s5608y','?????','?????????',NULL,NULL,'general','coze',NULL,'gpt-3.5-turbo',0.70,2000,0,1,NULL,'2025-07-30 01:22:54','2025-07-31 09:55:48','test-key','test-bot','test-user',NULL,1,5,NULL,NULL),('mdot6gntccoxka0dn0n','????','????????????',NULL,NULL,'writing','coze',NULL,'gpt-3.5-turbo',0.70,2000,0,0,NULL,'2025-07-30 01:26:19','2025-07-30 16:36:17','writing-key','writing-bot','writing-user',NULL,1,4,NULL,NULL),('mdpi8ky2jhq5j1sfoy','测试智能体','这是一个用于测试grid布局的智能体','这是一个专业的文案仿写智能体，可以帮助您：\n1. 分析原文的结构和风格\n2. 生成高质量的仿写内容\n3. 保持原文的语气和情感\n4. 确保内容原创性','/favicon.png','general','coze',NULL,'gpt-3.5-turbo',0.70,2000,0,9,NULL,'2025-07-30 13:07:48','2025-08-01 18:29:29','pat_3MmZhqAZY2UQKo1N5O3Ilx5EqE8SwiutwP8yjCItkFqZNqjm30FZYsLHUUpaue5y','7522395536545415183','wkhgogogo1',NULL,1,5,'[\"帮我仿写一篇营销文案\", \"分析这段文字的写作风格\", \"生成类似风格的内容\", \"优化文案的表达效果\"]','https://e.tb.cn/h.hM7pnr9MfEL414U?tk=9x5045uDmAI'),('mdpi9z0qgvm7a0ngc3b','去2331','21321',NULL,NULL,'general','coze',NULL,'gpt-3.5-turbo',0.70,2000,0,0,NULL,'2025-07-30 13:08:53','2025-07-30 16:36:17','Aa123456','请问请问','wkhgogogo1',NULL,0,5,NULL,NULL),('mdpjd30bdj84zmtgj6d','测试写作助手','按时间当年u脑神经科学才能结婚的丈夫v',NULL,NULL,'general','coze',NULL,'gpt-3.5-turbo',0.70,2000,0,0,NULL,'2025-07-30 13:39:18','2025-07-30 17:56:27','pat_3MmZhqAZY2UQKo1N5O3Ilx5EqE8SwiutwP8yjCItkFqZNqjm30FZYsLHUUpaue5y','7522395536545415183','wkhgogogo1',NULL,1,5,NULL,NULL),('mdpjpquswepuel80c6s','权威的','大晚上的',NULL,NULL,'general','coze',NULL,'gpt-3.5-turbo',0.70,2000,0,1,NULL,'2025-07-30 13:49:09','2025-07-30 18:30:54',NULL,NULL,NULL,NULL,0,5,NULL,NULL),('mdpk4ezijzy5fxs26v','测试智能体（用户调试）','由 @微甜 z（小红📕）原创的「文案仿写二创（同结构、文风、字数）」AI 智能体，搭载 DeepSeek 大模型【📕店 /🍑店搜 “微甜 z” 免费试用】。\n\n只要给我：\n你需要仿写的标题及文案 + 改写需求（支持文件上传，word/pdf/txt）\n我就能：\n生成一篇高质量仿写二创文案 + 10 个不同平台爆款标题 + 不满意重新生成\n\n😏全网独家技能：\n✔ 真人口语化，去 AI 味；\n✔ 保持与原文案同结构、文风、字数一致，字数不会大范围缩水；\n✔ 支持各类文案（自媒体文案、公众号文章、商业计划书等）\n✔支持字数扩写。\n\n请输入暗号 “文案改写” 进行会话。','请问请问','/agents_shortcut/mdpk4ezijzy5fxs26v.png','business','coze',NULL,'gpt-3.5-turbo',0.70,2000,0,49,NULL,'2025-07-30 14:00:33','2025-08-01 20:25:13','pat_3MmZhqAZY2UQKo1N5O3Ilx5EqE8SwiutwP8yjCItkFqZNqjm30FZYsLHUUpaue5y','7522395536545415183','wkhgogogo1',NULL,1,10,'[\"犬瘟热发AEF\", \"*****************我去士大夫随风倒v\"]',NULL),('mdpzzlazymzzyjoi9wp','测试图标和提示词功能','这是一个用于测试图标上传和预设提示词功能的智能体',NULL,'/favicon.png','general','builtin',NULL,'gpt-3.5-turbo',0.70,2000,0,0,NULL,'2025-07-30 21:24:42','2025-08-01 18:19:33',NULL,NULL,NULL,NULL,1,3,'[\"你是一个专业的AI助手，请帮助用户解决问题\", \"123123123\"]','https://e.tb.cn/h.hM7pnr9MfEL414U?tk=9x5045uDmAI'),('mdrd5nqnht9t1xmkhve','测试内置智能体','这是一个用于测试内置智能体功能的智能体',NULL,'/favicon.png','general','builtin','你是一个专业的AI助手，专门用于测试内置智能体功能。请根据用户的问题提供准确、有用的回答。','gpt-3.5-turbo',0.70,2000,0,2,NULL,'2025-07-31 20:21:06','2025-08-01 09:31:37',NULL,NULL,NULL,'测试暗号',1,5,'[\"q2121323123\"]',NULL),('mdrd7yzbvu1s7jlmmiq','测试智能体（内置）','由 @微甜 z（小红📕）原创的「文案仿写二创（同结构、文风、字数）」AI 智能体，搭载 DeepSeek 大模型【📕店 /🍑店搜 “微甜 z” 免费试用】。\n\n只要给我：\n你需要仿写的标题及文案 + 改写需求（支持文件上传，word/pdf/txt）\n我就能：\n生成一篇高质量仿写二创文案 + 10 个不同平台爆款标题 + 不满意重新生成\n\n😏全网独家技能：\n✔ 真人口语化，去 AI 味；\n✔ 保持与原文案同结构、文风、字数一致，字数不会大范围缩水；\n✔ 支持各类文案（自媒体文案、公众号文章、商业计划书等）\n✔支持字数扩写。\n\n请输入暗号 “文案改写” 进行会话。',NULL,'/favicon.png','media','builtin','由 @微甜 z（小红📕）原创的「文案仿写二创（同结构、文风、字数）」AI 智能体，搭载 DeepSeek 大模型【📕店 /🍑店搜 “微甜 z” 免费试用】。\n\n只要给我：\n你需要仿写的标题及文案 + 改写需求（支持文件上传，word/pdf/txt）\n我就能：\n生成一篇高质量仿写二创文案 + 10 个不同平台爆款标题 + 不满意重新生成\n\n😏全网独家技能：\n✔ 真人口语化，去 AI 味；\n✔ 保持与原文案同结构、文风、字数一致，字数不会大范围缩水；\n✔ 支持各类文案（自媒体文案、公众号文章、商业计划书等）\n✔支持字数扩写。\n\n请输入暗号 “文案改写” 进行会话。','gpt-3.5-turbo',0.70,2000,0,6,NULL,'2025-07-31 20:22:54','2025-08-01 20:08:42',NULL,NULL,NULL,'AI做作',1,10,'[\"123123123\"]','https://e.tb.cn/h.hM7pnr9MfEL414U?tk=9x5045uDmAI');
/*!40000 ALTER TABLE `agents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ai_provider_configs`
--

DROP TABLE IF EXISTS `ai_provider_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_provider_configs` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `enabled` tinyint(1) DEFAULT '0',
  `api_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `base_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `default_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `temperature` decimal(3,2) DEFAULT '0.70',
  `max_tokens` int DEFAULT '4096',
  `extra_config` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_provider` (`user_id`,`provider`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_provider` (`provider`),
  CONSTRAINT `ai_provider_configs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_provider_configs`
--

LOCK TABLES `ai_provider_configs` WRITE;
/*!40000 ALTER TABLE `ai_provider_configs` DISABLE KEYS */;
INSERT INTO `ai_provider_configs` VALUES ('mdpeqdwu3dpzb0t1a6j','mdoi3uqorptmrc7zwr','google',1,'8ab5b1d999a8f486e4505fbf9ed986c8:6729328398b459fb992413b13d66a89ae92bcc509b1a3e0f8b4e881dc7eb3c8463bb6774a45e9b3490adabde0803b11f','https://generativelanguage.googleapis.com','gemini-2.5-pro',0.70,4096,NULL,'2025-07-30 11:29:41','2025-07-30 13:03:26'),('mdpez446ja9vk0vtsyi','mdpexo3f13xs4mlm3wsi','google',1,'6f4f6f030ddc1231c2760013f661ded9:83e6c888c7bf59932009f8436528861354dfddb8db19bdec6d7ac6c05d8a5ae94c0c118bac92d44d56288fee02eb6d5c6ffdeb237e2fa5cc064f7a561e7e4882','https://www.dpapi.top/v1','gemini-2.5-pro',0.70,4096,'{\"fullApiUrl\": \"https://www.dpapi.top/v1/v1beta/models\"}','2025-07-30 11:36:28','2025-08-01 19:10:52'),('mdpf19y7kycb8y65n6','mdoi3uqorptmrc7zwr','openai',0,'b971e07f45c74726d41b8d28129ac95a:9794b94fd8c3a33d85e3bd6516045bfb53c406e861b4946f1d7ae691faeafdde','https://api.openai.com/v1','GPT-4o',0.70,4096,NULL,'2025-07-30 11:38:09','2025-07-31 20:33:51'),('mdpfava46p5yrzdo8lr','mdoi3uqorptmrc7zwr','anthropic',0,'714fad855d34246f812655341795ecff:c3e13ae2ca471cde4812ce08aa7fb88d09dc64d9eb4cceba805cdd0d9a3ef5fc','https://api.anthropic.com','claude-4-0',0.70,4096,NULL,'2025-07-30 11:45:36','2025-07-30 11:45:36'),('mdqy6hb10gwodsth9c1t','mdpexo3f13xs4mlm3wsi','openai',1,'c0ea8c6a7f77a997e65ac316ba980a06:30dad21d46a6a55947397fefdcbd6380ed89ad43fa1175aa8c415325e84c89da54744a9b8141b3cf4efd2361ae9038237647e1d792127941cd23e677abcc33fb23ade6ee30c5ff432ff4cd4e5b8a0c86e2ce1a55c94927c7308017db01677632b70015851d5e3c53699de4afe8aa29c5868bb3992eaa5f1cefe69c38110c1d922554066c75ec315aa0d32410cf8f3a6cc88b3e51bb829402f44e2fb35103406256506f7d10faec83a263997c6531eedc','https://api.openai.com','GPT-4o',0.70,4096,'{\"fullApiUrl\": \"https://api.openai.com/chat/completions\"}','2025-07-31 13:21:50','2025-08-01 19:11:08'),('mdqzlao4zzun9k716pd','mdpexo3f13xs4mlm3wsi','deepseek',1,'c57b3bd8359987eea314c2dedd623b4c:ace20823d6c8212275647fc608908761bf042a11f38bdf47ec61674187d6ef487838ef1677d47a3f9edbc9e4ba5c98ae','https://api.deepseek.com','deepseek-chat',0.70,4096,'{\"fullApiUrl\": \"https://api.deepseek.com/chat/completions\"}','2025-07-31 14:01:21','2025-08-01 19:11:54'),('mdramx0jb2bk5ryms9','mdpexo3f13xs4mlm3wsi','anthropic',1,'16ec4b65344b27dfc75f17e9dbdc4ce3:e27e80c8f77babf0b90d780701543f60ca9860b47bc27917125eef09c79594f5299898f6fabfa31874e179cb8af1fb48bbc16167216d288c00f77e677669e678','https://www.dpapi.top/v1','claude-sonnet-4-20250514',0.70,4096,'{\"fullApiUrl\": \"https://www.dpapi.top/v1/chat/completions\"}','2025-07-31 19:10:33','2025-08-01 19:11:26'),('mdtqql3d3xw89enk6lf','mdsypxigx3y803qn0x','google',1,'ed18092f1cc1b7fef2c6e267c92569d0:a14350442c2a7dd08936902f133581d6f46f5a81756ecf441d62d6e27b5b674f12b1d8b8a03f1a1bf43961d657bffd8b2672cc0a30d850ed13ce3cd31fe85d14','https://www.dpapi.top/v1','gemini-2.5-pro',0.70,4096,'{\"fullApiUrl\": \"https://www.dpapi.top/v1\"}','2025-08-02 12:16:50','2025-08-02 12:16:58');
/*!40000 ALTER TABLE `ai_provider_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_verification_codes`
--

DROP TABLE IF EXISTS `email_verification_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_verification_codes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_verification_codes`
--

LOCK TABLES `email_verification_codes` WRITE;
/*!40000 ALTER TABLE `email_verification_codes` DISABLE KEYS */;
INSERT INTO `email_verification_codes` VALUES (2,'<EMAIL>','859043','2025-07-29 11:47:19','2025-07-29 11:37:19'),(4,'<EMAIL>','492802','2025-07-29 12:04:15','2025-07-29 11:54:15'),(11,'<EMAIL>','204436','2025-08-01 23:44:24','2025-08-01 23:34:25');
/*!40000 ALTER TABLE `email_verification_codes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token` (`token`),
  KEY `idx_expires_at` (`expires_at`),
  CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_reset_tokens`
--

LOCK TABLES `password_reset_tokens` WRITE;
/*!40000 ALTER TABLE `password_reset_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_reset_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `prompts`
--

DROP TABLE IF EXISTS `prompts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prompts` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'general',
  `tags` json DEFAULT NULL,
  `is_favorite` tinyint(1) DEFAULT '0',
  `usage_count` int DEFAULT '0',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `prompts`
--

LOCK TABLES `prompts` WRITE;
/*!40000 ALTER TABLE `prompts` DISABLE KEYS */;
INSERT INTO `prompts` VALUES ('hppth18xu','修复后的标题测试','修复后的内容测试','custom','[\"API\", \"测试\"]',1,0,NULL,'2025-07-29 20:28:09','2025-07-31 20:40:16'),('test-1','测试提示词','这是一个测试提示词的内容','custom','[\"测试\", \"示例\"]',0,0,NULL,'2025-07-29 20:25:39','2025-07-30 11:32:38');
/*!40000 ALTER TABLE `prompts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `redemption_batches`
--

DROP TABLE IF EXISTS `redemption_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `redemption_batches` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code_type` enum('duration','usage') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_count` int NOT NULL,
  `used_count` int DEFAULT '0',
  `duration_days` int DEFAULT NULL,
  `usage_count` int DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_code_type` (`code_type`),
  CONSTRAINT `redemption_batches_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `redemption_batches_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `redemption_batches`
--

LOCK TABLES `redemption_batches` WRITE;
/*!40000 ALTER TABLE `redemption_batches` DISABLE KEYS */;
INSERT INTO `redemption_batches` VALUES ('mdrfscgbpi0kuqfun3','mdrd7yzbvu1s7jlmmiq','123123_2025-07-31_10个','duration','123123',10,0,2,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44'),('mdrg453j22k9f7ianz5','mdrd7yzbvu1s7jlmmiq','测试兑换码_2025-07-31_10个','duration','测试兑换码',10,0,30,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54'),('mdrhc0i86qrupnklwbl','mdrd7yzbvu1s7jlmmiq','123123_2025-07-31_10个','duration','123123',10,0,2,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01'),('mdrholjhdq54we3i4j8','mdrd7yzbvu1s7jlmmiq','223123_2025-07-31_10个','duration','223123',10,1,3,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-08-01 10:10:32'),('mdrhqmdwwksgxnuzrh','mdrd7yzbvu1s7jlmmiq','123_2025-07-31_10个','duration','123',10,7,4,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-08-01 10:15:54'),('mdri7b5rit6wvd9wpoo','mdpk4ezijzy5fxs26v','213312_2025-07-31_10个','usage','213312',10,1,NULL,20,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-08-01 09:07:23'),('mdrliswgt8zighf3fqh','mdrd7yzbvu1s7jlmmiq','123123213_2025-07-31_10个','usage','123123213',10,2,NULL,22,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 09:50:49'),('mds4kggqxlfencfm10i','mdpk4ezijzy5fxs26v','你好_2025-08-01_10个','duration','你好',10,2,6,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:11:37');
/*!40000 ALTER TABLE `redemption_batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `redemption_codes`
--

DROP TABLE IF EXISTS `redemption_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `redemption_codes` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code_type` enum('duration','usage') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `duration_days` int DEFAULT NULL,
  `usage_count` int DEFAULT NULL,
  `hash_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `salt` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `used_at` timestamp NULL DEFAULT NULL,
  `used_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_used` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_code` (`code`),
  KEY `idx_code_type` (`code_type`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `used_by` (`used_by`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_is_used` (`is_used`),
  CONSTRAINT `redemption_codes_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `redemption_codes_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `redemption_codes_ibfk_3` FOREIGN KEY (`used_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `redemption_codes_ibfk_4` FOREIGN KEY (`batch_id`) REFERENCES `redemption_batches` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `redemption_codes`
--

LOCK TABLES `redemption_codes` WRITE;
/*!40000 ALTER TABLE `redemption_codes` DISABLE KEYS */;
INSERT INTO `redemption_codes` VALUES ('mdrfscgeyltm35bm3ss','mdrd7yzbvu1s7jlmmiq','RC-ZQRF-9GHU','duration','123123',2,NULL,'7d3277bd3fd3908eda6415bf4290ebb39e35d66237f9ce984f35edd9ebc06816','46657baf9020e910d12b4bb13de8ebb6',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscggt4i2xo06f9','mdrd7yzbvu1s7jlmmiq','RC-R4Y7-KFQ2','duration','123123',2,NULL,'977d7b89f0979f224ce610e4b40eeb81f0eacf611619ff2aa36de5a8993d36cd','9a6ba4298cd78b540aa1698a9a38d084',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscghpw3z6yekq3','mdrd7yzbvu1s7jlmmiq','RC-WTXH-JFRV','duration','123123',2,NULL,'50d4d589bd243c1a81b3b67305b46d6fa4d9c525eabfb2153ce7932192248459','370ede6feb9f5fa99126e39fd07e83e3',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscgj687xmcbmc74','mdrd7yzbvu1s7jlmmiq','RC-PLN4-QPDV','duration','123123',2,NULL,'219d479ea9bad605f300520ed0ed94f134ae915fec01f799642330e66d54d8b3','8953ad84000cde8d5a977509dc1f3e4a',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscgkoyw864l3mio','mdrd7yzbvu1s7jlmmiq','RC-K9FC-4FVP','duration','123123',2,NULL,'460dc57f9a48461762bf4a3db098d49b43f1bcc3fe88f8641c4f88441c7d920a','6a05d207bdbbf36142d6580044e55c65',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscglgbq9ombkbgs','mdrd7yzbvu1s7jlmmiq','RC-CQVF-K33K','duration','123123',2,NULL,'704d15b1b0ee132cf434eff495e81f2009f4e3e0037e5c91bbe4e816b9d9c5d2','e04431ae2a5fc30f827b994da9c00c90',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscgnosphdzedtn','mdrd7yzbvu1s7jlmmiq','RC-564T-KDBQ','duration','123123',2,NULL,'a3117f5acc667aa22c62b7dc8c64681fc581462f5084a83564dbe5220aaaa691','e4c39c17629cadf73d2db58bae1e3049',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscgotqrizhg38qp','mdrd7yzbvu1s7jlmmiq','RC-3WN5-E9LY','duration','123123',2,NULL,'a17bfad6a5152e61eebb50e8ac7d4a8538c334ac2640faf45b0c3e1ccc6031b0','737eb844523147e3868f10f883325f05',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscgpspf4473ze0e','mdrd7yzbvu1s7jlmmiq','RC-HXS7-QAZ5','duration','123123',2,NULL,'289cab4f538e84b03e327376b62811308e10c62ce6d9a9faf982d455858f3372','c193de8b70f5376791af7860cda61d92',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrfscgrcghd7qsjtvn','mdrd7yzbvu1s7jlmmiq','RC-N56U-H8BP','duration','123123',2,NULL,'f6f1ddea4379ca00a21be226048c2c5ac02be2c724ddadf38b2d62c36fbf73b7','e0fae40296542074f57f5fff7893d3cb',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:34:44','2025-07-31 21:34:44','mdrfscgbpi0kuqfun3',0),('mdrg453mvrhpxkj5slo','mdrd7yzbvu1s7jlmmiq','RC-M7F4-745G','duration','测试兑换码',30,NULL,'61a6ee4cb06cea4ac81ab1fdda28314b7da49e4d75902f258a4ebb8d32ec2198','77f71bc4608036ef126d89244e1d4941',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453o3nzbpn8u5xt','mdrd7yzbvu1s7jlmmiq','RC-V8E7-WQWJ','duration','测试兑换码',30,NULL,'bec02963af40d4768e8ece4e44d8fb06212f5d2b3bbce146fac00b46c16bc906','dc9162e9a39166c353d9ef3c9ce8eba6',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453pqf8li3oto5a','mdrd7yzbvu1s7jlmmiq','RC-LGB8-FLDS','duration','测试兑换码',30,NULL,'a30c5aa10eb7f0d23cb058d3df5ba239db6251efca07473cd91ddc69c3c99b5a','6884e4808e04afcde2c65697a0bd6fbf',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453rikcrzdzixpe','mdrd7yzbvu1s7jlmmiq','RC-JDZ9-M4VM','duration','测试兑换码',30,NULL,'7d67e9c0bb11da0c5f5f2b30ca84f2ac14d5fb0bbd3f200dd19cb36ce155f844','dd01f037241c84d26e74f8a6a60bbf31',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453sr5xx3236m3m','mdrd7yzbvu1s7jlmmiq','RC-7LUG-WRHX','duration','测试兑换码',30,NULL,'1f659f7e2ba336c7e37c4d283b67146130a4c12df10793232ec864cbc99ccb46','1bdf19d79fd7a0898fb0cdddd2866a4a',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453toejskzjs0al','mdrd7yzbvu1s7jlmmiq','RC-JV8G-5LJ4','duration','测试兑换码',30,NULL,'07902154e6e64266072d2fdb4907ae34a6572996866db92eac6e6fba6873825f','eaddc4058058bbcc83e0985e17b8ea65',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453vhh5enr89cbl','mdrd7yzbvu1s7jlmmiq','RC-2SJP-FPW4','duration','测试兑换码',30,NULL,'c7bfc0e3a8bfab07ab4af31841b2a931d39168520dc22615cc6a48364efd71cb','a405957a68832f48c05bbdfaba89190c',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453wtl2g8r45k9h','mdrd7yzbvu1s7jlmmiq','RC-9FJ5-ZLZH','duration','测试兑换码',30,NULL,'fee8603bec3309de91b8d0f693a24980b11eb5297e86e26a251041c665884cfb','8ecbf459b31e7e33f29be5e6c1503699',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453yqtm0oaodswo','mdrd7yzbvu1s7jlmmiq','RC-A6AH-NQ86','duration','测试兑换码',30,NULL,'dc01941edb5c2209d280211db1fce241a96cbd024bde463536e4341d6b8281e4','d4c9192b2d45bf5178d1eab016ce0202',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrg453zuovgkgoanw','mdrd7yzbvu1s7jlmmiq','RC-JSVW-B9SW','duration','测试兑换码',30,NULL,'69b30e2cb06d4177fdef0c2d7b027fffd30132a66fc4e52fcbe3e35be87c6070','8a7865969c490d76bf0e55e09a8d2b22',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 21:43:54','2025-07-31 21:43:54','mdrg453j22k9f7ianz5',0),('mdrhc0ici29hkt2snhs','mdrd7yzbvu1s7jlmmiq','RC-EZKA-ZGR4','duration','123123',2,NULL,'1dc02fd71d0213c10aba672043cd717855a281fce46181d869ad121b8682ce09','82dc588a59126b61fb726ea66eb268d0',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0idtirzl9m2x3n','mdrd7yzbvu1s7jlmmiq','RC-N5K4-VVG2','duration','123123',2,NULL,'0ca28a1a08eda294d4d9a6de942c7b86feea670d5cbcce0749a167f606289566','807c8c487573836aa531519ccac187d1',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0ifhgh8i1apwjn','mdrd7yzbvu1s7jlmmiq','RC-94CH-74JA','duration','123123',2,NULL,'063de9c99ff484d26410e2490d336b6f37501695eb64dca57cfc64406a8f591e','779cbdff70859052bd2b530f194fefe7',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0ig1jphcym20ra','mdrd7yzbvu1s7jlmmiq','RC-9X93-SZM7','duration','123123',2,NULL,'42246c8ca71c8f01a3f12c48c7690c6d9f29a323f7e4bed17ed32a9c15e33635','87f8d9ae7ff3b5b823780e7701d59757',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0iie44x68au9nt','mdrd7yzbvu1s7jlmmiq','RC-RWLM-5AET','duration','123123',2,NULL,'790bfad43919e491e0574a9425c4ecb1720c87a5339dfcaf5e55899c07c3e2aa','8118730ca95009ca2bf15a7806e7693c',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0ijxtnilpqwsi','mdrd7yzbvu1s7jlmmiq','RC-X56P-ANU6','duration','123123',2,NULL,'faa417e533ae464ce0c484da6e86b30e1913197f23c91b7614c23b86a25aa23a','5c0625b9e4c5a916ca10dfbaa5e55457',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0ik4ba1omgzy1r','mdrd7yzbvu1s7jlmmiq','RC-PYFW-6RUC','duration','123123',2,NULL,'a3c74ba2cea987c4eae33d4d0c60f0da67c88dc8c5d0d33571f37efdcff8291c','4a509512f297da5a35d6316518f79e11',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0imnaxaqme0kbq','mdrd7yzbvu1s7jlmmiq','RC-9EYA-CQ7N','duration','123123',2,NULL,'29892fcae27ff69cfd1f0360931ccc8672a5dedd1b32fd3090578dc65e9c4514','2f1602d2794e58173ea3bc40732d6ba4',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0indv2qa7581s9','mdrd7yzbvu1s7jlmmiq','RC-8PJ2-PLD2','duration','123123',2,NULL,'206698533b1279848b1bd6f05132037b2745c7334c34f5bf7b26c4d05ee0b7c2','f546883eb3ed62f6a33d03b75e2c4aa7',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrhc0iocfc1em7njok','mdrd7yzbvu1s7jlmmiq','RC-6WBR-K678','duration','123123',2,NULL,'5a15d7eefcc7f4578c6d337adf7e070e3cad552c27f5417e63458b43d18683bc','909fc2a3fe639b31952c16df4a452e25',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:18:01','2025-07-31 22:18:01','mdrhc0i86qrupnklwbl',0),('mdrholjj95zs18jzoyc','mdrd7yzbvu1s7jlmmiq','RC-UPEH-HKV9','duration','223123',3,NULL,'bcc3d398618e4b2b7bc92dbe78897c262ceb768fa2f05e4ceec3bdc0040103db','c8aac438baee19d49ff0f260964f6aa4',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrholjlqxmz2ldplz','mdrd7yzbvu1s7jlmmiq','RC-W3GH-Q2Y8','duration','223123',3,NULL,'1f1c09b7772b3368c465d96d06906bfdd0abe30f0ead2cd06cdda8e3d42f7d7d','f11d275866097a99925e43dca48ccfbf',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrholjpoijjatzy358','mdrd7yzbvu1s7jlmmiq','RC-F396-CG24','duration','223123',3,NULL,'a72dcf609e59d1936d094f4df5346537728c9948b7e37fe266c6adf8a4cb3500','2309a56dba51ce11249a4856801edf4f','2025-08-01 10:10:32','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-08-01 10:10:32','mdrholjhdq54we3i4j8',1),('mdrholjqjq0i65me2l','mdrd7yzbvu1s7jlmmiq','RC-5VQ7-HGFA','duration','223123',3,NULL,'0fd7da4f8823d440608d54abea27b1ca89a53e4c57150b01dedd2727567a5224','17f0885d7f6c103b72949880eaa6cd70',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrholjsbxsj4rtqco','mdrd7yzbvu1s7jlmmiq','RC-LKWN-5YZY','duration','223123',3,NULL,'bbf78eef42bc5efd11ce1da3233abd5ed96c0bf91d74ff607151482ac0adab49','b481545034f8fe706ff19292f3c0bd6c',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrholjtm81yimscpn8','mdrd7yzbvu1s7jlmmiq','RC-LYD5-HM59','duration','223123',3,NULL,'8452342226e813b1812a61e79da95fcabc78e9e8211b7e1bfbdf8cd9578c81fc','04ca5869cd6f0754adecca5bfbf2850a',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrholjvxvtevx1xes','mdrd7yzbvu1s7jlmmiq','RC-4QWB-F7F6','duration','223123',3,NULL,'891a69efcea3242f2ad67728dec18f666112c613d60b0b2dbe2a8f9f0729f154','a68733d3d6d3820c3de721533a942f08',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrholjxz2gui4udpgh','mdrd7yzbvu1s7jlmmiq','RC-8GK2-7VBG','duration','223123',3,NULL,'a166ef6847f71345052b46b6ac7fbfb20388bffd056313906b81f9d58f1a3aa9','1a7af21dab6cfbe86bc7e562ecfca95b',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrholjy2m79bgjowtu','mdrd7yzbvu1s7jlmmiq','RC-94XU-W648','duration','223123',3,NULL,'a6757d3f010aadefee33c3ecf6e005495be875551b3855e79f8dbe4712ad3cb7','9be42825b6bf9dfb67cd0a81422ae036',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrholk0c2oyco2mb2n','mdrd7yzbvu1s7jlmmiq','RC-USQT-LC6R','duration','223123',3,NULL,'dd884fa44a9f4b3add93d414c8f310938c1127f49c5b998bfc4dd3fc5cdf17f5','f9f59d1f91a93f14b3a10096854bcdf3',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:27:48','2025-07-31 22:27:48','mdrholjhdq54we3i4j8',0),('mdrhqmebhrynanq1hla','mdrd7yzbvu1s7jlmmiq','RC-3KHG-5S63','duration','123',4,NULL,'fbcb4354e7713518ac5e2c79809d5f1d8dab37958ab7524407d89407b77ad29c','78395eee39a1e25277913c857d386879','2025-07-31 23:19:46','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-07-31 23:19:46','mdrhqmdwwksgxnuzrh',0),('mdrhqmee3rb8w4yp3yi','mdrd7yzbvu1s7jlmmiq','RC-9YD5-D7MM','duration','123',4,NULL,'8ced5de69404aacd434bea55e19925e6bd8c3ea9301111bafdc1932da067c2e8','1e78e4dda3748519a857c32ac0733906','2025-07-31 23:39:54','mdoi3uqorptmrc7zwr',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-07-31 23:39:54','mdrhqmdwwksgxnuzrh',1),('mdrhqmeg44iz01ehmbb','mdrd7yzbvu1s7jlmmiq','RC-G7UP-Q7WD','duration','123',4,NULL,'fb7ff5b446178653e47ed6c1d96e2e81be50f140690650df737167ff95e272e4','6d736ffe48b970856cb6b7bf768ed0bb','2025-07-31 23:42:25','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-07-31 23:42:25','mdrhqmdwwksgxnuzrh',1),('mdrhqmeinrhg0tnhzl','mdrd7yzbvu1s7jlmmiq','RC-EX6S-A2CS','duration','123',4,NULL,'a3a82643ca6cca093b0ca8a695b39207776be4e44934d6d16d64df4624febc40','0f509b14ad993543b05a875b769bb405','2025-08-01 00:01:17','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-08-01 00:01:17','mdrhqmdwwksgxnuzrh',1),('mdrhqmemzjqghcz4u2','mdrd7yzbvu1s7jlmmiq','RC-QUBH-GQT2','duration','123',4,NULL,'0b45f0a6eaaa44341e416f541eeac3f08b47598ee27f53bb185846c23e582745','bf0bd390b0cf3d0dec1c768f82ef662f',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-07-31 22:29:23','mdrhqmdwwksgxnuzrh',0),('mdrhqmepw4aiblxkmtc','mdrd7yzbvu1s7jlmmiq','RC-TH7L-3F6W','duration','123',4,NULL,'ee6b904e971c3e4bc3e0c4a585edca3447de991f527c466da696ba17dfd20e77','cf1ce5862766780da5d69bff62672450','2025-07-31 23:51:43','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-07-31 23:51:43','mdrhqmdwwksgxnuzrh',1),('mdrhqmeswk4kximifi','mdrd7yzbvu1s7jlmmiq','RC-RYSR-Q7JT','duration','123',4,NULL,'40681155b177e0df0f056469299e4e77fd5ebb2b6642088512ceb967ff8568eb','5be19cef2afc8d088d497078aca6c0e0','2025-08-01 10:03:36','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-08-01 10:03:36','mdrhqmdwwksgxnuzrh',1),('mdrhqmewyfiy1kvbdt','mdrd7yzbvu1s7jlmmiq','RC-H5K5-63FT','duration','123',4,NULL,'e0d078454f26eae7cee4057d5ce5fcd8902addd736553fabb57a8de15fa63d09','f329da756420ea11dbcb02df521fe04f','2025-08-01 10:15:54','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-08-01 10:15:54','mdrhqmdwwksgxnuzrh',1),('mdrhqmeyr3g5emwgtj','mdrd7yzbvu1s7jlmmiq','RC-H96D-AT9U','duration','123',4,NULL,'cd3216a0168135b821a572f31434d054f0c7f3b7bbf41483df3603c9b9d790a3','62b0715a132d083b0ed2ca02f4120f8e',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-07-31 22:29:23','mdrhqmdwwksgxnuzrh',0),('mdrhqmfn71t0stmf6b3','mdrd7yzbvu1s7jlmmiq','RC-PGM9-4APW','duration','123',4,NULL,'7ca651d9571fde063d3248e13837aafe2c875b6ae4053207cfb1a8c0f20456ad','63b8452d719f1001720c930219ef0915',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:29:23','2025-07-31 22:29:23','mdrhqmdwwksgxnuzrh',0),('mdri7b5uai4klzaqsug','mdpk4ezijzy5fxs26v','RC-DDJH-ZJ97','usage','213312',NULL,20,'b5039a62c23da35d4451023f5692dc9e27bab35826fac28d78a9914088e61afd','839f5053a71362d60c4a5096a15667f7','2025-07-31 23:07:17','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 23:07:17','mdri7b5rit6wvd9wpoo',0),('mdri7b5v4mftocw162o','mdpk4ezijzy5fxs26v','RC-6PBX-4BGZ','usage','213312',NULL,20,'14b58df7062c98554a73f3909d2df5f932dfd93f5885133fa1735f917a13ae07','edc0584727cab3ac90c692ab5214a65e','2025-08-01 09:07:23','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-08-01 09:07:23','mdri7b5rit6wvd9wpoo',1),('mdri7b5x06yu2gu7fqi6','mdpk4ezijzy5fxs26v','RC-W95T-TUAB','usage','213312',NULL,20,'27b01ee1be60fe24a893096616c3ee12fb2de767938dcd4eef0fcc6a957668ad','cbce33318f23e0d6566ddc81567c7b99',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 22:42:21','mdri7b5rit6wvd9wpoo',0),('mdri7b5ynd0efotl47','mdpk4ezijzy5fxs26v','RC-P6TN-VMCW','usage','213312',NULL,20,'a23692de4e50acb531759f4dffcb5d287e381fd371cf34ed2a079faa53eb9096','a66bcab994ae105facaa42faf41c7917',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 22:42:21','mdri7b5rit6wvd9wpoo',0),('mdri7b5z6086djmtbdo','mdpk4ezijzy5fxs26v','RC-FYC8-6RLW','usage','213312',NULL,20,'8ea5f1131e3bd1ccddda69f327b2873ebd3130969ec6d54ad124bbc00d77fa94','24366543cb7b00ebbd55a7f7bd082179',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 22:42:21','mdri7b5rit6wvd9wpoo',0),('mdri7b61pjls99vkq','mdpk4ezijzy5fxs26v','RC-LXE8-H3L7','usage','213312',NULL,20,'fd76c0a9105997c94137d36a0fa5daf6f5bfeb04b7ae1e5b83d687a9a67925ee','3abbf643df73e7d8a9e7c63b34a654f8',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 22:42:21','mdri7b5rit6wvd9wpoo',0),('mdri7b63uq3c27dxo7j','mdpk4ezijzy5fxs26v','RC-9HMB-LJTQ','usage','213312',NULL,20,'c8edce43cdb4b0395706fcf016b96d14087646ee685a2cc6f0dc6c9282183d94','bf91678d4d96197ec0d724d8db261d66',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 22:42:21','mdri7b5rit6wvd9wpoo',0),('mdri7b6458ycnoq5yot','mdpk4ezijzy5fxs26v','RC-92FG-3YHR','usage','213312',NULL,20,'a44a2e0ff3aa49ba7deb9edbb319a1e60f98e62eb9a3e4201e10b13adb8cae5a','459f34986269667d1db8b61cec201dff',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 22:42:21','mdri7b5rit6wvd9wpoo',0),('mdri7b66nckwrqlwc4j','mdpk4ezijzy5fxs26v','RC-CWLK-QRKJ','usage','213312',NULL,20,'92f351c21dc2b437ec8175fc3ce8861bfd24011b987a6dc284064e5176c076e6','906e7e5303ec8e0a2b9800bd7d52dad1',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 22:42:21','mdri7b5rit6wvd9wpoo',0),('mdri7b68tlj3za6iqx','mdpk4ezijzy5fxs26v','RC-RJ5T-5PHZ','usage','213312',NULL,20,'2ffd6108347b6f70eea8f842650a8194ac8260c575152f8722bd3a649d641669','9c56d0e84389332d638211c63894135e',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-07-31 22:42:21','2025-07-31 22:42:21','mdri7b5rit6wvd9wpoo',0),('mdrliswigfyxd83e7ys','mdrd7yzbvu1s7jlmmiq','RC-539J-RZ6R','usage','123123213',NULL,22,'89e4026dce125d5f30d61593402c1ceba7dd00673f35d4b0db7526dd93f647ca','38bbf309402edd1551aae166c030af55','2025-08-01 00:15:28','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:28','mdrliswgt8zighf3fqh',1),('mdrliswktrrqbh7zjw','mdrd7yzbvu1s7jlmmiq','RC-8DLB-GALC','usage','123123213',NULL,22,'9070f4c5e287734c8361a54ae70481db70e55df005c3c9d8292538a1ca26ae2c','803e778d59a8203c8b88e2787d959171',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:16','mdrliswgt8zighf3fqh',0),('mdrliswmzhs2u6r2bf','mdrd7yzbvu1s7jlmmiq','RC-6DKS-NQKQ','usage','123123213',NULL,22,'a30c8fdf7633359f12366ffdebf5f8ec44de752107300bb42daf85dddf9979c7','5442a9a7ffe5fd41735e8f5f911b8251','2025-08-01 09:50:49','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 09:50:49','mdrliswgt8zighf3fqh',1),('mdrliswnmcryi46ujtp','mdrd7yzbvu1s7jlmmiq','RC-DHGN-N5NL','usage','123123213',NULL,22,'f53cedf0114b3ef43e2fde7c58b86f0218fed4b90e4f3b89c5a883be6a86d482','70e9804120da049dc9c8c1a3e0035e55',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:16','mdrliswgt8zighf3fqh',0),('mdrliswoapajtvxhp07','mdrd7yzbvu1s7jlmmiq','RC-4GN5-C97X','usage','123123213',NULL,22,'bd9fe9551853229815c4b47366e310f44141001be00b2a608c6910b190af8c46','5271e11559ca596793642aa0bdb73d68',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:16','mdrliswgt8zighf3fqh',0),('mdrlisx8s24rk0fewf','mdrd7yzbvu1s7jlmmiq','RC-QGTC-KJSK','usage','123123213',NULL,22,'e4bc3ee8191137f9583aafa847dddc87a1c44acdd8edc2b0b58ebd1b08167161','441c0e4dd92e7a5a9d5186f369146192',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:16','mdrliswgt8zighf3fqh',0),('mdrlisxakz9678ziks','mdrd7yzbvu1s7jlmmiq','RC-8D7M-NL2R','usage','123123213',NULL,22,'97fd081d9056a3eefdd372043548cfc0395f4e2e1918f1e43bdaf3831ab96037','34b62100bb66a40a24ba6b87d655ab8e',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:16','mdrliswgt8zighf3fqh',0),('mdrlisxb9fbc77bvgwv','mdrd7yzbvu1s7jlmmiq','RC-SM69-VWBU','usage','123123213',NULL,22,'06ee8996a6d1ba66cd51263a5fbcef5fa2f0bc80fb685071fc14ebd3bad210ba','1eeded954f39f44f278eb7c1dbc59e42',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:16','mdrliswgt8zighf3fqh',0),('mdrlisxd9t9d33b2a5p','mdrd7yzbvu1s7jlmmiq','RC-ADQC-SFGB','usage','123123213',NULL,22,'a5475e5d17d3b19fc8cd9bfa086de28179ae9cf9f2afd2cc783f68a4da6a51ed','f286703fcd7fab40c2353e051cc53c71',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:16','mdrliswgt8zighf3fqh',0),('mdrlisxetnjotuvwxb','mdrd7yzbvu1s7jlmmiq','RC-HGGH-YJJ7','usage','123123213',NULL,22,'170017e447c2b0f5c3f8e4f54f668393802068dd6555ada38b1fc57ce483f289','dfe16f34bced2862d76695618346e4f7',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 00:15:16','2025-08-01 00:15:16','mdrliswgt8zighf3fqh',0),('mds4kggtjl2ot1jcg9f','mdpk4ezijzy5fxs26v','RC-JCFF-ZJJE','duration','你好',6,NULL,'8fd2c71e64de17ef7e7ebcd4b9f5b30faa45beed267749e1c5c4ebca6289534b','bd134a9cc10e757b657cae94a7392eb3','2025-08-01 09:08:41','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:41','mds4kggqxlfencfm10i',1),('mds4kggvq6oc3zt9y1g','mdpk4ezijzy5fxs26v','RC-YBUH-734G','duration','你好',6,NULL,'cc050247a4abecebca21c817f1ec5c338a58a00d1a5f95d9ca92c75489f28fa5','b0648fe972d2412b1d154e8464f0318b','2025-08-01 09:11:37','mdpexo3f13xs4mlm3wsi',NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:11:37','mds4kggqxlfencfm10i',1),('mds4kggw6aywzp01i2o','mdpk4ezijzy5fxs26v','RC-TA7V-LMVD','duration','你好',6,NULL,'f1064a1d36e8bcaba242e3c52f9106cf9d5b3b5226d59285f44e9459625a4bd3','ef555617ecb2e8fa5d47da2849ca3ef2',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:26','mds4kggqxlfencfm10i',0),('mds4kggxldmkli2zadn','mdpk4ezijzy5fxs26v','RC-7MQ9-UDTQ','duration','你好',6,NULL,'0c078e8c62e42354a9b8f4a8c5439672e28f9af7d5d2dfe391ca8ec43ef796c9','1f0a1b2ee1b052e6ff8d263e86beb718',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:26','mds4kggqxlfencfm10i',0),('mds4kggzh2e6u61csvu','mdpk4ezijzy5fxs26v','RC-WNTM-DYGA','duration','你好',6,NULL,'f0d4d13ad4ecf51d87b0098b259ebafa50da42236ac7904e221e4aed0647cafe','83f4344068d60cfc0d9d726988f1101a',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:26','mds4kggqxlfencfm10i',0),('mds4kgh0p5lq0f22o5','mdpk4ezijzy5fxs26v','RC-FRCZ-KUP5','duration','你好',6,NULL,'e3d3eecdce33890c050fafed59d7809e0a897287ba4185cb1bd931df6f10e1b4','0ca97136fc2377eb8ff97a09b8ee65cc',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:26','mds4kggqxlfencfm10i',0),('mds4kgh10luowkt095rk','mdpk4ezijzy5fxs26v','RC-BHQR-QRQM','duration','你好',6,NULL,'65f29d766a363dc7e321b36446d260f2c90c95397cf847cd54fff357790ea8db','93043c73ac22623486d8b682fb67bca7',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:26','mds4kggqxlfencfm10i',0),('mds4kgh3hbqlwbh8qt','mdpk4ezijzy5fxs26v','RC-PYX7-PELW','duration','你好',6,NULL,'d150af7b3aa3dc92f2d765290d525db0ded20ab6e4357788c44f8ce294e97e9d','9f0dc03c160264f8cd375e1ad8972adb',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:26','mds4kggqxlfencfm10i',0),('mds4kgh457skz2wbcqa','mdpk4ezijzy5fxs26v','RC-QMU2-5KEJ','duration','你好',6,NULL,'f9d4a217be4da7a7a401399b70b0b0b59db632915696d99017fb2b87647abb52','79460b59460678cf08b9eb74a152e988',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:26','mds4kggqxlfencfm10i',0),('mds4kgh6qn7taiy207','mdpk4ezijzy5fxs26v','RC-QHCB-7JVS','duration','你好',6,NULL,'dbbc1e2a6b6792152fed0b2eec736de8a88ee1bc14f2715ecda591bee8cee730','b239bf83a078ce88b8a321509c3dd3ad',NULL,NULL,NULL,'mdoi3uqorptmrc7zwr','2025-08-01 09:08:26','2025-08-01 09:08:26','mds4kggqxlfencfm10i',0);
/*!40000 ALTER TABLE `redemption_codes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `redemption_logs`
--

DROP TABLE IF EXISTS `redemption_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `redemption_logs` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `redemption_code_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code_type` enum('duration','usage') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `duration_days` int DEFAULT NULL,
  `usage_count` int DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `redeemed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_redemption_code_id` (`redemption_code_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_redeemed_at` (`redeemed_at`),
  CONSTRAINT `redemption_logs_ibfk_1` FOREIGN KEY (`redemption_code_id`) REFERENCES `redemption_codes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `redemption_logs_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `redemption_logs_ibfk_3` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `redemption_logs`
--

LOCK TABLES `redemption_logs` WRITE;
/*!40000 ALTER TABLE `redemption_logs` DISABLE KEYS */;
INSERT INTO `redemption_logs` VALUES ('mdrjjes2wgn1yq8yi1c','mdrhqmebhrynanq1hla','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','duration',4,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-31 23:19:46'),('mdrk9bha5k0pscrmk3r','mdrhqmee3rb8w4yp3yi','mdoi3uqorptmrc7zwr','mdrd7yzbvu1s7jlmmiq','duration',4,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-31 23:39:54'),('mdrkcjocny0qhg82do','mdrhqmeg44iz01ehmbb','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','duration',4,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-31 23:42:25'),('mdrkoincu82o54yesp','mdrhqmepw4aiblxkmtc','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','duration',4,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-07-31 23:51:43'),('mdrl0t7qqqnlmz979hk','mdrhqmeinrhg0tnhzl','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','duration',4,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 00:01:17'),('mdrlj1pyno8kl8rznvg','mdrliswigfyxd83e7ys','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','usage',NULL,22,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 00:15:28'),('mds4j3z1m0otmsm2mo','mdri7b5v4mftocw162o','mdpexo3f13xs4mlm3wsi','mdpk4ezijzy5fxs26v','usage',NULL,20,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 09:07:23'),('mds4ks38m8etl84fmxq','mds4kggtjl2ot1jcg9f','mdpexo3f13xs4mlm3wsi','mdpk4ezijzy5fxs26v','duration',6,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 09:08:41'),('mds4ojr5g4juaz9imfg','mds4kggvq6oc3zt9y1g','mdpexo3f13xs4mlm3wsi','mdpk4ezijzy5fxs26v','duration',6,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 09:11:37'),('mds62y7xyg18a5cxz6i','mdrliswmzhs2u6r2bf','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','usage',NULL,22,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 09:50:49'),('mds6jdx49bgpqitprj','mdrhqmeswk4kximifi','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','duration',4,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 10:03:36'),('mds6sbg6a946y2o2bcg','mdrholjpoijjatzy358','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','duration',3,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 10:10:32'),('mds6z7asr6ztvsk104','mdrhqmewyfiy1kvbdt','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','duration',4,NULL,'::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-01 10:15:54');
/*!40000 ALTER TABLE `redemption_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_agent_subscriptions`
--

DROP TABLE IF EXISTS `user_agent_subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_agent_subscriptions` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `agent_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `pricing_plan_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `remaining_usage` int DEFAULT '0',
  `total_usage` int DEFAULT '0',
  `start_date` timestamp NULL DEFAULT NULL,
  `end_date` timestamp NULL DEFAULT NULL,
  `status` enum('active','expired','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `purchase_price` decimal(10,2) NOT NULL,
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'CNY',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_agent_plan` (`user_id`,`agent_id`,`pricing_plan_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_pricing_plan_id` (`pricing_plan_id`),
  KEY `idx_status` (`status`),
  KEY `idx_end_date` (`end_date`),
  CONSTRAINT `user_agent_subscriptions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_agent_subscriptions_ibfk_2` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_agent_subscriptions_ibfk_3` FOREIGN KEY (`pricing_plan_id`) REFERENCES `agent_pricing_plans` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_agent_subscriptions`
--

LOCK TABLES `user_agent_subscriptions` WRITE;
/*!40000 ALTER TABLE `user_agent_subscriptions` DISABLE KEYS */;
INSERT INTO `user_agent_subscriptions` VALUES ('mdrkoingmenblkg40oo','mdpexo3f13xs4mlm3wsi','mdrd7yzbvu1s7jlmmiq','redemption_duration',43,5,'2025-07-31 23:51:43','2025-08-05 10:15:54','active',0.00,'CNY','2025-07-31 23:51:43','2025-08-01 20:08:42'),('mds4ojrahd4ba1zb7c','mdpexo3f13xs4mlm3wsi','mdpk4ezijzy5fxs26v','redemption_duration',0,8,'2025-08-01 09:11:37','2025-08-07 09:11:37','active',0.00,'CNY','2025-08-01 09:11:37','2025-08-01 20:25:14');
/*!40000 ALTER TABLE `user_agent_subscriptions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_settings`
--

DROP TABLE IF EXISTS `user_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_settings` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `current_provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'google',
  `current_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'gemini-2.5-flash',
  `theme` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'light',
  `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'zh-CN',
  `font_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'medium',
  `auto_save` tinyint(1) DEFAULT '1',
  `notifications` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_settings`
--

LOCK TABLES `user_settings` WRITE;
/*!40000 ALTER TABLE `user_settings` DISABLE KEYS */;
INSERT INTO `user_settings` VALUES ('mdpei150pz6zx7fzyw','mdoi3uqorptmrc7zwr','google','gemini-2.5-flash-preview-05-20',NULL,NULL,NULL,NULL,NULL,'2025-07-30 11:23:11','2025-08-01 11:29:47'),('mdpexxir3rylgrt1svq','mdpexo3f13xs4mlm3wsi','google','gemini-2.5-pro',NULL,NULL,NULL,NULL,NULL,'2025-07-30 11:35:33','2025-08-01 11:22:48'),('mdsz69nbh4oo9ovxgi','mdsz5eb8wd2uok0451','google','gemini-2.5-flash-preview-05-20','light','zh-CN','medium',1,1,'2025-08-01 23:25:12','2025-08-01 23:25:12'),('mdtqani4ef7l93evkve','mdsypxigx3y803qn0x','google','gemini-2.5-pro',NULL,NULL,NULL,NULL,NULL,'2025-08-02 12:04:27','2025-08-02 12:17:06');
/*!40000 ALTER TABLE `user_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT '0',
  `verification_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verification_expires_at` timestamp NULL DEFAULT NULL,
  `reset_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reset_expires_at` timestamp NULL DEFAULT NULL,
  `last_login_at` timestamp NULL DEFAULT NULL,
  `login_count` int DEFAULT '0',
  `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'local',
  `provider_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `role` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'user' COMMENT '用户角色：user-普通用户，admin-管理员',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('mdoi3uqorptmrc7zwr','wkhgogogo1','<EMAIL>','$2b$12$m7pcxtCYeJmfrv0q4Gvb1eqgDV0ZJ6yTbJGEvmEJjAHc/igjoN04K',NULL,1,'b6a7cd1ac7c7f805275bef09f9b660e9155c3a0d857ee6ef53486568180d8e4f','2025-07-30 20:16:22',NULL,NULL,'2025-08-01 17:11:53',91,'local',NULL,'2025-07-29 20:16:22','2025-08-01 17:11:53','admin'),('mdpexo3f13xs4mlm3wsi','www','<EMAIL>','$2b$12$YddnFGVERGw14FjCvxck9uDsTGV6XBj512gOQFYuH2/YqX4ZjkdvG',NULL,1,'4b7eeb7cc0949590098a6f8cf6296ef24a35d42ec531f323ebad77e5f66e8bdf','2025-07-31 11:35:20',NULL,NULL,'2025-08-02 00:28:16',68,'local',NULL,'2025-07-30 11:35:20','2025-08-01 16:28:15','admin'),('mdsypxigx3y803qn0x','微甜948','<EMAIL>','$2b$12$JnZU5iAa9DMzs9wlhZoRYeAhqXI/oFBUfcX/PqgmjpKndF87A..c.',NULL,1,'f127d2415551f036a72fa301728a3c6c87375d47ec1979c7977b55033366f7ec','2025-08-02 23:12:30',NULL,NULL,'2025-08-02 12:14:52',2,'local',NULL,'2025-08-01 23:12:30','2025-08-02 04:14:52','admin'),('mdsz5eb8wd2uok0451','1825822788','<EMAIL>','$2b$12$sHYAm0fQWr090rqCbILF4OANXXRJUOQOFI4rMH9fZ4tMSA.omtyLy',NULL,1,'61a7f4efb85fb8117544b1dd7cc01ae97c8eefd034887032c0b5a2aa4c3a57b3','2025-08-02 23:24:32',NULL,NULL,'2025-08-01 23:25:08',1,'local',NULL,'2025-08-01 23:24:32','2025-08-01 15:25:08','user');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-02 12:20:31
